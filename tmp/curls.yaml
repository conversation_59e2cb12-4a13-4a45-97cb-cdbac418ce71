curl -X POST \
 http://localhost:8080/send/text \
 -H 'Content-Type: application/json' \
 -d '{
   "phone": "5541988481092",
   "message": "O<PERSON><PERSON>, esta é uma mensagem de teste! Glória a Deus!!!\\o/",
   "delayTyping": "5"
 }'
 curl -X GET \
 https://api.z-api.io/instances/3E42757CD8F1E0636C9E5E3E11E99CA6/token/0FE1F8824B2827B3C4F3C7FF/restart \
 --header 'client-token: Fdb98bd1b944d4970888ddaa69d40fbccS'
 https://api.z-api.io/instances/SUA_INSTANCIA/token/SEU_TOKEN/restart
 curl --request POST \
  --url https://api.z-api.io/instances/3E42757CD8F1E0636C9E5E3E11E99CA6/token/0FE1F8824B2827B3C4F3C7FF/send-text \
  --header 'client-token: Fdb98bd1b944d4970888ddaa69d40fbccS' \
  --header 'content-type: application/json' \
  --data '{"phone": "5541988481092", "message": "Glória a Deus!!!\\o/"}'

curl -X POST \
      http://localhost:8080/send/image \
        -H 'Content-Type: application/json' \
        -d '{
          "phone": "+5531995980442",
          "image":
          "https://images.a12.com/source/files/c/282548/Bulma_Amaral_-_Leao_de_Juda_-793353_1280-847-0-0.jpg",
          "caption": "Leão da tribo de Judá"
        }'

      sleep_time=3
      while true; do
        echo "Executando comando..."
        curl -X POST \
          http://localhost:8080/send/text \
          -H 'Content-Type: application/json' \
          -d '{
        "phone": "+5531995980442",
        "message": "Eu te amo!!! 😍❤️😍"
          }'

        sleep $sleep_time
        sleep_time=$((sleep_time + 1))
        echo "Próximo timer será de " $sleep_time "segundos"
done