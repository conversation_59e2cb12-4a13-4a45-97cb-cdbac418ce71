[package]
name = "rust-version"
version = "0.1.0"
edition = "2024"



[dependencies]
actix-web = "4"
dotenv = "0.15"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
env_logger = "0.9"
async-trait = "0.1"
reqwest = { version = "0.11", features = ["json"] }
bb8-redis = "0.15"
bb8 = "0.8"

redis = { version = "0.25", features = ["tokio-comp"] }
apalis = { version = "0.5", features = ["tokio-comp"] }
apalis-redis = "0.5"
apalis-cron = "0.5"
tower = { version = "0.4", features = ["util"] }


