use apalis::prelude::{<PERSON>, Worker<PERSON><PERSON>er, WorkerFactory, Request, <PERSON><PERSON><PERSON>, TokioExecutor};
use apalis_redis::RedisStorage;
use tower::service_fn;
use std::io;

use crate::scheduler::tasks::WhatsappMessage;
use crate::whatsapp::manager::Manager as WhatsappManager;
use crate::whatsapp::dto::send_message::SendMessageRequest;

async fn whatsapp_message_handler(job: Request<WhatsappMessage>, whatsapp_manager: WhatsappManager) -> Result<(), Error> {
    let job_data = job.inner();
    println!(" Processing message for {}: '{}'", job_data.to, job_data.message);

    // Convert WhatsappMessage to SendMessageRequest
    let send_req = SendMessageRequest {
        to: job_data.to.clone(),
        message_type: "text".to_string(), // Assuming text for scheduled messages
        content: serde_json::json!({ "message": job_data.message.clone() }),
    };

    match whatsapp_manager.send_message(send_req).await {
        Ok(_) => {
            println!("Message sent successfully by worker!");
            Ok(())
        },
        Err(e) => {
            eprintln!("Error sending message from worker: {:?}", e);
            Err(Error::from(io::Error::new(io::ErrorKind::Other, e.to_string())))
        }
    }
}

pub async fn start_worker(storage: RedisStorage<WhatsappMessage>, whatsapp_manager: WhatsappManager) {
    let whatsapp_worker = WorkerBuilder::new("whatsapp-messages")
        .with_storage(storage.clone())
        .build(service_fn(move |job| {
            let manager_clone = whatsapp_manager.clone();
            async move {
                whatsapp_message_handler(job, manager_clone).await
            }
        }));

    Monitor::<TokioExecutor>::new()
        .register(whatsapp_worker)
        .run()
        .await
        .expect("Error running apalis monitor");
}