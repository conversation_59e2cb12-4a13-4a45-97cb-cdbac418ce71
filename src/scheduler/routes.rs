use actix_web::{web, HttpRequest, HttpResponse, Responder};
use serde::Deserialize;
use chrono::{DateTime, Utc};
use std::env;
use apalis_redis::RedisStorage;
use apalis::prelude::Storage;

use crate::scheduler::tasks::WhatsappMessage;

#[derive(Deserialize)]
pub struct ScheduleRequest {
    send_at: String,
    to: String,
    message: String,
}

pub async fn schedule_whatsapp_message(
    req: web::Json<ScheduleRequest>,
    http_req: HttpRequest,
    storage: web::Data<RedisStorage<WhatsappMessage>>,
) -> impl Responder {
    if let Some(api_key) = http_req.headers().get("x-api-key") {
        let expected_api_key = env::var("TEMPORARY_API_KEY").expect("TEMPORARY_API_KEY must be set");
        if api_key.to_str().unwrap_or_default() != expected_api_key {
            return HttpResponse::Unauthorized().finish();
        }
    } else {
        return HttpResponse::Unauthorized().finish();
    }

    let send_at_dt = match DateTime::parse_from_rfc3339(&req.send_at) {
        Ok(dt) => dt.with_timezone(&Utc),
        Err(_) => return HttpResponse::BadRequest().body("Invalid send_at format. Use ISO 8601 (RFC3339)"),
    };

    let whatsapp_message = WhatsappMessage { 
        to: req.to.clone(), 
        message: req.message.clone(),
        send_at: send_at_dt,
    };

    let mut storage = storage.get_ref().clone();

    // Agendando a tarefa para ser executada no futuro
    // apalis schedule expects a Unix timestamp (i64)
    match storage.schedule(whatsapp_message, send_at_dt.timestamp()).await {
        Ok(job_id) => HttpResponse::Accepted().json(serde_json::json!({
            "status": "accepted",
            "job_id": job_id.to_string()
        })),
        Err(e) => {
            eprintln!("Error scheduling job: {:?}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}
