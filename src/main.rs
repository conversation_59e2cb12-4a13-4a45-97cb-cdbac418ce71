use actix_web::{App, HttpServer, web};
use dotenv::dotenv;
use std::env;
use std::sync::Arc;
use tokio::task;

mod scheduler;
mod whatsapp;

use apalis_redis::RedisStorage;
use redis::{Client, aio::ConnectionManager as RedisConnectionManager};
use scheduler::routes::schedule_whatsapp_message;
use scheduler::tasks::WhatsappMessage;
use scheduler::worker::start_worker;
use whatsapp::integrations::zapi::service::ZapiService;
use whatsapp::integrations::zapi::webhooks;
use whatsapp::manager::Manager as WhatsappManager;
use whatsapp::routes::send_whatsapp_message;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    // --- Configurar o Storage para a API ---
    let redis_url = env::var("REDIS_URL").expect("REDIS_URL must be set");
    let client = Client::open(redis_url.as_str()).unwrap();
    let connection_manager = RedisConnectionManager::new(client).await.unwrap();
    let storage = RedisStorage::<WhatsappMessage>::new(connection_manager);

    // --- Configurar o Manager do WhatsApp (se necessário) ---
    let port = env::var("PORT").unwrap_or_else(|_| "8081".to_string());
    let z_api_instance_id = env::var("Z_API_INSTANCE_ID").unwrap_or_default();
    let z_api_instance_token = env::var("Z_API_INSTANCE_TOKEN").unwrap_or_default();
    let z_api_security_token = env::var("Z_API_SECURITY_TOKEN").unwrap_or_default();

    let whatsapp_manager = if !z_api_instance_id.is_empty() {
        let zapi_service = ZapiService::new(
            z_api_instance_id,
            z_api_instance_token,
            z_api_security_token,
        );
        WhatsappManager::new(Arc::new(zapi_service))
    } else {
        println!("WhatsApp provider not configured. Skipping...");
        // Provide a dummy manager if not configured, or handle this case differently
        // For now, creating a manager with a dummy provider that always returns an error
        WhatsappManager::new(Arc::new(whatsapp::provider::DummyWhatsappProvider))
    };

    // --- Iniciar o Worker em uma task separada ---
    let worker_storage = storage.clone();
    let worker_whatsapp_manager = whatsapp_manager.clone();
    task::spawn(async move {
        start_worker(worker_storage, worker_whatsapp_manager).await;
    });

    println!("🚀 HTTP server starting on port {}...", port);

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(storage.clone()))
            .app_data(web::Data::new(whatsapp_manager.clone()))
            .configure(webhooks::register_webhooks)
            .service(
                web::resource("/schedule/whatsapp")
                    .route(web::post().to(schedule_whatsapp_message)),
            )
            .service(web::resource("/send/whatsapp").route(web::post().to(send_whatsapp_message)))
    })
    .bind(format!("0.0.0.0:{}", port))?
    .run()
    .await
}
