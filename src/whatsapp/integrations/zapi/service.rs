use async_trait::async_trait;
use reqwest::Client;
use serde_json::json;

use crate::whatsapp::dto::send_message::SendMessageRequest;
use crate::whatsapp::provider::WhatsappProvider;

pub struct ZapiService {
    client: Client,
    instance_id: String,
    instance_token: String,
    security_token: String,
}

impl ZapiService {
    pub fn new(instance_id: String, instance_token: String, security_token: String) -> Self {
        ZapiService {
            client: Client::new(),
            instance_id,
            instance_token,
            security_token,
        }
    }
}

#[async_trait]
impl WhatsappProvider for ZapiService {
    async fn send_message(&self, req: SendMessageRequest) -> Result<(), Box<dyn std::error::Error>> {
        let base_url = format!(
            "https://api.z-api.io/instances/{}/token/{}",
            self.instance_id,
            self.instance_token
        );

        let (url, payload) = match req.message_type.as_str() {
            "text" => {
                let url = format!("{}/send-text", base_url);
                let payload = json!({
                    "phone": req.to,
                    "message": req.content.get("message").and_then(|v| v.as_str()).unwrap_or("")
                });
                (url, payload)
            }
            "image" => {
                let url = format!("{}/send-image", base_url);
                let mut payload_map = serde_json::Map::new();
                payload_map.insert("phone".to_string(), json!(req.to));
                if let Some(image) = req.content.get("image").and_then(|v| v.as_str()) {
                    payload_map.insert("image".to_string(), json!(image));
                }
                if let Some(caption) = req.content.get("caption").and_then(|v| v.as_str()) {
                    payload_map.insert("caption".to_string(), json!(caption));
                }
                (url, serde_json::Value::Object(payload_map))
            }
            "document" => {
                let url = format!("{}/send-document", base_url);
                let mut payload_map = serde_json::Map::new();
                payload_map.insert("phone".to_string(), json!(req.to));
                if let Some(document) = req.content.get("document").and_then(|v| v.as_str()) {
                    payload_map.insert("document".to_string(), json!(document));
                }
                if let Some(file_name) = req.content.get("fileName").and_then(|v| v.as_str()) {
                    payload_map.insert("fileName".to_string(), json!(file_name));
                }
                (url, serde_json::Value::Object(payload_map))
            }
            _ => return Err(Box::new(std::io::Error::new(std::io::ErrorKind::Other, "Unsupported message type"))),
        };

        let res = self.client.post(&url)
            .header("Content-Type", "application/json")
            .header("Client-Token", &self.security_token)
            .json(&payload)
            .send()
            .await?;

        if res.status().is_success() {
            Ok(())
        } else {
            let status = res.status().as_u16();
            let body = res.text().await.unwrap_or_else(|_| "<no body>".to_string());
            Err(Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Z-API returned non-OK status: {} - {}", status, body))))
        }
    }
}
