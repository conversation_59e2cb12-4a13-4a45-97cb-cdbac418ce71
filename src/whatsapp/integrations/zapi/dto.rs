use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct OnChatPresenceDTO {
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    pub phone: String,
    pub status: String, // "online", "offline", "typing"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnConnectDTO {
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    pub phone: String,
    pub connected: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnDisconnectedDTO {
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    pub error: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnMessageReceived {
    pub id: String,
    pub message: Message,
    pub timestamp: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Message {
    pub from: String,
    pub text: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnMessageSendDTO {
    #[serde(rename = "messageId")]
    pub message_id: String,
    pub phone: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnMessageStatusChangesDTO {
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    pub ids: Vec<String>,
    pub status: String, // "read", "delivered", "sent"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OnMessageDTO {
    #[serde(rename = "instanceId")]
    pub instance_id: String,
    #[serde(rename = "messageId")]
    pub message_id: String,
    pub phone: String,
    pub text: String,
    pub timestamp: i64,
}
