use actix_web::{web, HttpResponse, Responder};

use crate::whatsapp::integrations::zapi::dto::*;

async fn log_payload<T: serde::Serialize>(
    module: &str,
    event: &str,
    filename: &str,
    payload: T,
) -> Result<(), std::io::Error> {
    let data = serde_json::to_string_pretty(&payload)?;
    println!("--- Logging Payload ---");
    println!("Module: {}", module);
    println!("Event: {}", event);
    println!("Filename: {}", filename);
    println!("Payload:\n{}", data);
    println!("--- End Logging Payload ---");
    Ok(())
}

pub fn register_webhooks(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/webhooks/zapi")
            .route("/on-connect", web::post().to(on_connect_webhook))
            .route("/on-message-send", web::post().to(on_message_send_webhook))
            .route("/on-disconnected", web::post().to(on_disconnected_webhook))
            .route(
                "/on-message-status-changes",
                web::post().to(on_message_status_changes_webhook),
            )
            .route("/on-chat-presence", web::post().to(on_chat_presence_webhook))
            .route(
                "/on-message-received",
                web::post().to(on_message_received_webhook),
            ),
    );
}

async fn on_connect_webhook(payload: web::Json<OnConnectDTO>) -> impl Responder {
    let payload = payload.into_inner();
    println!(
        "Successfully connected instance {} for phone {}",
        payload.instance_id,
        payload.phone
    );
    let filename = format!("{}-{}", payload.instance_id, payload.phone);
    if let Err(e) = log_payload("whatsapp", "on-connect", &filename, &payload).await {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}

async fn on_message_send_webhook(payload: web::Json<OnMessageSendDTO>) -> impl Responder {
    let payload = payload.into_inner();
    println!("Message {} sent to {}", payload.message_id, payload.phone);
    if let Err(e) = log_payload("whatsapp", "on-message-send", &payload.message_id, &payload).await
    {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}

async fn on_disconnected_webhook(payload: web::Json<OnDisconnectedDTO>) -> impl Responder {
    let payload = payload.into_inner();
    println!(
        "Instance {} disconnected: {}",
        payload.instance_id,
        payload.error
    );
    let filename = format!("{}", payload.instance_id);
    if let Err(e) = log_payload("whatsapp", "on-disconnected", &filename, &payload).await {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}

async fn on_message_status_changes_webhook(
    payload: web::Json<OnMessageStatusChangesDTO>,
) -> impl Responder {
    let payload = payload.into_inner();
    println!(
        "Message status changed for {} to {}",
        payload.ids[0],
        payload.status
    );
    let filename = format!("{}-{}", payload.ids[0], payload.status);
    if let Err(e) =
        log_payload("whatsapp", "on-message-status-changes", &filename, &payload).await
    {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}

async fn on_chat_presence_webhook(payload: web::Json<OnChatPresenceDTO>) -> impl Responder {
    let payload = payload.into_inner();
    println!("Chat presence for {}: {}", payload.phone, payload.status);
    let filename = format!("{}-{}", payload.phone, payload.status);
    if let Err(e) = log_payload("whatsapp", "on-chat-presence", &filename, &payload).await {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}

async fn on_message_received_webhook(payload: web::Json<OnMessageReceived>) -> impl Responder {
    let payload = payload.into_inner();
    println!(
        "Message {} received from {}: {}",
        payload.id,
        payload.message.from,
        payload.message.text
    );
    let filename = format!("{}-{}", payload.id, payload.message.from);
    if let Err(e) = log_payload("whatsapp", "on-message-received", &filename, &payload).await {
        eprintln!("Error saving log: {}", e);
        return HttpResponse::InternalServerError().finish();
    }
    HttpResponse::Ok().finish()
}
