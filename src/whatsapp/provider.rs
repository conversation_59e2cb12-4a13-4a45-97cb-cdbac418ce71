use async_trait::async_trait;
use crate::whatsapp::dto::send_message::SendMessageRequest;

#[async_trait]
pub trait WhatsappProvider: Send + Sync {
    async fn send_message(&self, req: SendMessageRequest) -> Result<(), Box<dyn std::error::Error>>;
}

pub struct DummyWhatsappProvider;

#[async_trait]
impl WhatsappProvider for DummyWhatsappProvider {
    async fn send_message(&self, _req: SendMessageRequest) -> Result<(), Box<dyn std::error::Error>> {
        Err("WhatsApp provider not configured.".into())
    }
}