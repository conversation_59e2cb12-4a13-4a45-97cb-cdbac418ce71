use crate::whatsapp::provider::WhatsappProvider;
use crate::whatsapp::dto::send_message::SendMessageRequest;
use std::sync::Arc;

pub struct Manager {
    provider: Arc<dyn WhatsappProvider + Send + Sync>,
}

impl Clone for Manager {
    fn clone(&self) -> Self {
        Manager {
            provider: self.provider.clone(),
        }
    }
}

impl Manager {
    pub fn new(provider: Arc<dyn WhatsappProvider + Send + Sync>) -> Self {
        Manager { provider }
    }

    pub async fn send_message(&self, req: SendMessageRequest) -> Result<(), Box<dyn std::error::Error>> {
        self.provider.send_message(req).await
    }
}
