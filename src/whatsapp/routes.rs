use actix_web::{web, HttpResponse, Responder, HttpRequest};
use crate::whatsapp::manager::Manager as WhatsappManager;
use crate::whatsapp::dto::send_message::SendMessageRequest;
use std::env;

pub async fn send_whatsapp_message(
    req: web::Json<SendMessageRequest>,
    http_req: HttpRequest,
    whatsapp_manager: web::Data<WhatsappManager>,
) -> impl Responder {
    println!("Received request for /send/whatsapp");

    if let Some(api_key) = http_req.headers().get("x-api-key") {
        let expected_api_key = env::var("TEMPORARY_API_KEY").expect("TEMPORARY_API_KEY must be set");
        if api_key.to_str().unwrap_or_default() != expected_api_key {
            println!("Unauthorized: Invalid API Key");
            return HttpResponse::Unauthorized().finish();
        }
    } else {
        println!("Unauthorized: Missing API Key");
        return HttpResponse::Unauthorized().finish();
    }

    println!("API Key validated. Attempting to send message to: {}", req.to);

    match whatsapp_manager.send_message(req.into_inner()).await {
        Ok(_) => {
            println!("Message sent successfully!");
            HttpResponse::Ok().json(serde_json::json!({ "status": "success", "message": "Message sent" }))
        },
        Err(e) => {
            eprintln!("Error sending message: {:?}", e);
            HttpResponse::InternalServerError().body(format!("Failed to send message: {}", e))
        }
    }
}